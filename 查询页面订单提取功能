// ==UserScript==
// @name         微信小商店订单提取助手
// @namespace    http://tampermonkey.net/
// @version      1.6
// @description  在微信小商店订单详情页提取订单编号、时间、状态和物流信息。已修复订单编号提取及Shadow DOM问题。
// <AUTHOR> Name
// @match        https://store.weixin.qq.com/shop/order/detail?orderid=*
// @grant        GM_addStyle
// @grant        GM_setValue
// @grant        GM_getValue
// @license      MIT
// ==/UserScript==

(function() {
    'use strict';

    // --- 1. 注入CSS样式 ---
    GM_addStyle(`
        #extractor-panel {
            position: fixed;
            top: 150px;
            right: 20px;
            width: 320px; /* 稍微加宽以适应更长的内容 */
            background-color: #f9f9f9;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            z-index: 9999;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
            transition: opacity 0.3s;
        }
        #extractor-header {
            padding: 10px 15px;
            background-color: #f1f1f1;
            border-bottom: 1px solid #ccc;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
        }
        #extractor-header h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }
        #extractor-body {
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }
        #extract-btn {
            padding: 8px 12px;
            background-color: #07c160;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: background-color 0.2s;
        }
        #extract-btn:hover {
            background-color: #06ad56;
        }
        #result-textarea {
            width: 100%;
            height: 180px; /* 增加高度以显示更多信息 */
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            line-height: 1.6;
            resize: vertical;
        }
        #toggle-btn {
            cursor: pointer;
            padding: 4px 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            font-size: 12px;
        }
        #toggle-btn:hover {
            background-color: #e9e9e9;
        }
        .panel-hidden #extractor-body {
            display: none;
        }
    `);

    // --- 2. 创建HTML面板 ---
    const panel = document.createElement('div');
    panel.id = 'extractor-panel';
    panel.innerHTML = `
        <div id="extractor-header">
            <h3>订单提取助手</h3>
            <button id="toggle-btn">收起</button>
        </div>
        <div id="extractor-body">
            <button id="extract-btn">提取信息</button>
            <textarea id="result-textarea" placeholder="提取结果将显示在这里..."></textarea>
        </div>
    `;
    document.body.appendChild(panel);

    // --- 3. 获取DOM元素 ---
    const extractBtn = document.getElementById('extract-btn');
    const resultTextarea = document.getElementById('result-textarea');
    const toggleBtn = document.getElementById('toggle-btn');


    // --- 4. 数据提取逻辑 (已修复订单编号提取问题) ---
    function extractOrderInfo() {
        let orderId = '未找到';
        let orderTime = '未找到';
        let orderStatus = '未找到';
        let carrier = '暂无';
        let trackingNumber = '暂无';
        let latestTrackingInfo = '暂无';

        // 步骤1: 找到 micro-app 宿主并进入 Shadow DOM
        const microApp = document.querySelector('micro-app[name="order"]');
        if (!microApp || !microApp.shadowRoot) {
            resultTextarea.value = "错误: 未能找到 micro-app 容器或其 Shadow DOM。";
            return;
        }
        const shadowRoot = microApp.shadowRoot;

        // 步骤2: 在 Shadow DOM 内部查找数据
        // 提取订单状态 (从页面顶部状态栏)
        const statusElement = shadowRoot.querySelector('.status_text span');
        if (statusElement) {
            orderStatus = statusElement.textContent.trim();
        }

        // 定位并提取“订单信息”和“物流信息”区块内容
        const allBlocks = shadowRoot.querySelectorAll('.weui-desktop-block');
        let orderInfoBlock = null;
        let logisticsBlock = null;

        allBlocks.forEach(block => {
            const titleElement = block.querySelector('.title');
            if (titleElement) {
                const titleText = titleElement.textContent.trim();
                if (titleText === '订单信息') {
                    orderInfoBlock = block;
                } else if (titleText === '物流信息') {
                    logisticsBlock = block;
                }
            }
        });

        // 从“订单信息”区块提取
        if (orderInfoBlock) {
            const infoCards = orderInfoBlock.querySelectorAll('.card');
            infoCards.forEach(card => {
                const labelElement = card.querySelector('.label');
                if (labelElement) {
                    const labelText = labelElement.textContent.trim();
                    const valueElement = card.querySelector('.value');
                    if (valueElement) {
                        switch (labelText) {
                            case '订单编号':
                                // **【修复】** 使用正则表达式匹配数字，更可靠
                                const match = valueElement.textContent.match(/\d+/);
                                if (match) {
                                    orderId = match[0];
                                }
                                break;
                            case '下单时间':
                                orderTime = valueElement.textContent.trim();
                                break;
                        }
                    }
                }
            });
        }

        // 从“物流信息”区块提取
        if (logisticsBlock) {
            const underLine = logisticsBlock.querySelector('.under_line');
            if (underLine) {
                const texts = underLine.querySelectorAll('p.text');
                if (texts.length >= 2) {
                    carrier = texts[0].textContent.trim();
                    trackingNumber = texts[1].textContent.replace('物流编号：', '').trim();
                }
            }

            const latestInfoEl = logisticsBlock.querySelector('.circle_green_tips .info_wrp');
            if (latestInfoEl) {
                const infoText = latestInfoEl.querySelector('p.text:nth-of-type(2)');
                const infoTime = latestInfoEl.querySelector('p.label.gray.small_num');
                if (infoText && infoTime) {
                    latestTrackingInfo = `${infoText.textContent.trim()} (${infoTime.textContent.trim()})`;
                } else if (infoText) {
                    latestTrackingInfo = infoText.textContent.trim();
                }
            }
        }

        const result = `订单编号：${orderId}\n下单时间：${orderTime}\n订单状态：${orderStatus}\n快递公司：${carrier}\n快递单号：${trackingNumber}\n快递最新信息：${latestTrackingInfo}`;
        resultTextarea.value = result;

        // 自动复制到剪贴板
        navigator.clipboard.writeText(result).then(() => {
            console.log('信息已复制到剪贴板');
            extractBtn.textContent = '提取成功，已复制！';
            setTimeout(() => { extractBtn.textContent = '提取信息'; }, 2000);
        }).catch(err => {
            console.error('复制失败: ', err);
            extractBtn.textContent = '提取成功，复制失败';
            setTimeout(() => { extractBtn.textContent = '提取信息'; }, 2000);
        });
    }


    // --- 5. 面板交互功能 ---

    // 折叠/展开功能
    function togglePanel() {
        panel.classList.toggle('panel-hidden');
        const isHidden = panel.classList.contains('panel-hidden');
        toggleBtn.textContent = isHidden ? '展开' : '收起';
        GM_setValue('panelHidden', isHidden);
    }

    // 拖动功能
    function makeDraggable(elmnt) {
        let pos1 = 0, pos2 = 0, pos3 = 0, pos4 = 0;
        const header = document.getElementById(elmnt.id + "-header");

        if (header) {
            header.onmousedown = dragMouseDown;
        } else {
            elmnt.onmousedown = dragMouseDown;
        }

        function dragMouseDown(e) {
            e = e || window.event;
            e.preventDefault();
            pos3 = e.clientX;
            pos4 = e.clientY;
            document.onmouseup = closeDragElement;
            document.onmousemove = elementDrag;
        }

        function elementDrag(e) {
            e = e || window.event;
            e.preventDefault();
            pos1 = pos3 - e.clientX;
            pos2 = pos4 - e.clientY;
            pos3 = e.clientX;
            pos4 = e.clientY;
            elmnt.style.top = (elmnt.offsetTop - pos2) + "px";
            elmnt.style.left = (elmnt.offsetLeft - pos1) + "px";
        }

        function closeDragElement() {
            document.onmouseup = null;
            document.onmousemove = null;
            GM_setValue('panelTop', elmnt.style.top);
            GM_setValue('panelLeft', elmnt.style.left);
        }
    }


    // --- 6. 初始化和事件绑定 ---

    // 绑定事件
    extractBtn.addEventListener('click', extractOrderInfo);
    toggleBtn.addEventListener('click', togglePanel);

    // 初始化面板状态和位置
    function initializePanel() {
        const savedTop = GM_getValue('panelTop', '150px');
        const savedLeft = GM_getValue('panelLeft', null);
        panel.style.top = savedTop;
        if (savedLeft) {
            panel.style.left = savedLeft;
            panel.style.right = 'auto';
        }

        const isHidden = GM_getValue('panelHidden', false);
        if (isHidden) {
            panel.classList.add('panel-hidden');
            toggleBtn.textContent = '展开';
        }

        makeDraggable(document.getElementById('extractor-panel'));
    }

    // 使用 MutationObserver 等待 micro-app 及其 Shadow DOM 加载完成
    const observer = new MutationObserver((mutations, obs) => {
        const microApp = document.querySelector('micro-app[name="order"]');
        if (microApp && microApp.shadowRoot) {
            // 进一步确认内容是否加载
            if (microApp.shadowRoot.querySelector('.weui-desktop-block')) {
                console.log('micro-app 的 Shadow DOM 及内容已准备好，初始化面板。');
                initializePanel();
                obs.disconnect(); // 找到后停止观察
            }
        }
    });

    // 启动观察器，监视整个文档的变化
    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    // 设置一个超时，以防万一
    setTimeout(() => {
        observer.disconnect();
        if (!panel.style.top || panel.style.top === '') {
             console.log('超时，强制初始化面板。');
             initializePanel();
        }
    }, 8000); // 8秒后超时

})();
