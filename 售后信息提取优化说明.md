# 售后信息提取优化说明

## 优化概述

根据"退款按钮页面提取功能"文档的代码，完全重构了售后详情页面的信息提取功能，确保能够精准提取订单售后状态、退款时间信息、退款金额与去向等关键信息。

## 主要优化内容

### 1. 完全基于文档的提取逻辑 📋

#### 核心提取函数
完全复制文档中验证过的提取函数：

```javascript
// 【文档方法】完全复制文档中的通用文本提取函数
const getText = (selector) => {
    const element = firstStepItem.querySelector(selector);
    return element ? element.innerText.trim() : '未找到';
};

// 【文档方法】完全复制文档中的详情查找函数
const findDetailByLabel = (label) => {
    const detailItems = firstStepItem.querySelectorAll('.block-item');
    for (const item of detailItems) {
        const titleElement = item.querySelector('.block-title');
        if (titleElement && titleElement.innerText.trim() === label) {
            const contentElement = item.querySelector('.block-content');
            return contentElement ? contentElement.innerText.trim() : '未找到内容';
        }
    }
    return '未找到标签';
};
```

#### 精确信息提取
按照文档中的精确方式提取信息：

```javascript
// 【精确提取】完全按照文档中的提取逻辑
const status = getText('.body .title');
const processingInfo = getText('.body .tips');
const refundAmountText = findDetailByLabel('退款金额');
const refundDestination = findDetailByLabel('钱款去向');

// 【赋值】按照文档的格式赋值
aftersaleStatus = status;
refundTime = processingInfo;
```

### 2. 信息组合策略 🔗

#### 退款金额与去向组合
按照文档的格式组合退款信息：

```javascript
// 【组合】按照文档的格式组合退款金额与去向
if (refundAmountText !== '未找到标签' && refundDestination !== '未找到标签') {
    refundAmount = `${refundAmountText} ${refundDestination}`;
} else if (refundAmountText !== '未找到标签') {
    refundAmount = refundAmountText;
} else if (refundDestination !== '未找到标签') {
    refundAmount = refundDestination;
} else {
    refundAmount = '未找到';
}
```

### 3. 增强备用提取方法 🛡️

#### 多层级备用策略
当主要方法失败时，使用增强的备用提取方法：

```javascript
// 1. 尝试查找任何步骤项
const allStepItems = shadowRoot.querySelectorAll('.step-item');

if (allStepItems.length > 0) {
    for (const stepItem of allStepItems) {
        // 在每个步骤项中使用文档方法
        const getText = (selector) => {
            const element = stepItem.querySelector(selector);
            return element ? element.innerText.trim() : null;
        };
        
        // 尝试提取信息...
    }
}
```

#### 全局选择器备用
如果步骤项中没有找到信息，使用全局选择器：

```javascript
// 全局售后状态提取
const globalStatusSelectors = [
    '.status_text span', '.aftersale-status', '[class*="status"]',
    '.title', '.status-title', '.main-status'
];

// 全局退款时间提取
const globalTimeSelectors = [
    '.refund-time', '.finish-time', '.complete-time', 
    '[class*="time"]', '.tips', '.process-info'
];

// 全局退款金额提取
const globalAmountSelectors = [
    '.refund-amount', '.amount', '[class*="money"]', 
    '[class*="amount"]', '.price', '.fee'
];
```

### 4. 详细状态监控 📊

#### 文档格式的输出
按照文档的格式输出提取结果：

```javascript
// 【验证】输出文档格式的提取结果
addStatusMonitorEvent(`文档格式提取结果:`, 'success');
addStatusMonitorEvent(`  - 订单售后状态：${aftersaleStatus}`, 'info');
addStatusMonitorEvent(`  - 退款时间信息：${refundTime}`, 'info');
addStatusMonitorEvent(`  - 退款金额与去向：${refundAmount}`, 'info');
```

### 5. 关键信息映射 🗺️

#### 信息字段对应关系

| 目标字段 | 文档选择器 | 备用选择器 | 说明 |
|---------|-----------|-----------|------|
| 订单售后状态 | `.body .title` | `.status_text span`, `.aftersale-status` | 售后处理状态 |
| 退款时间信息 | `.body .tips` | `.refund-time`, `[class*="time"]` | 退款完成时间 |
| 退款金额 | `findDetailByLabel('退款金额')` | `.refund-amount`, `[class*="money"]` | 退款金额数值 |
| 钱款去向 | `findDetailByLabel('钱款去向')` | 文本匹配 | 退款去向说明 |

### 6. 提取流程优化 🔄

#### 主要提取流程
```
1. 等待页面加载完成
2. 查找 .step .step-item:first-child 元素
3. 使用文档中的 getText() 函数提取状态和时间
4. 使用文档中的 findDetailByLabel() 函数提取金额和去向
5. 按照文档格式组合和赋值
6. 输出标准格式的提取结果
```

#### 备用提取流程
```
1. 查找所有 .step-item 元素
2. 在每个步骤项中使用文档方法尝试提取
3. 如果步骤项中未找到，使用全局选择器
4. 逐个尝试多种选择器直到找到信息
5. 记录详细的提取日志
```

### 7. 错误处理机制 🛠️

#### 多重保障
- **主要方法**: 使用文档验证的精确选择器
- **步骤项备用**: 在所有步骤项中查找信息
- **全局备用**: 使用全局选择器作为最后手段
- **详细日志**: 记录每一步的提取过程

#### 容错策略
```javascript
// 如果主要信息未找到，保持默认值
if (aftersaleStatus === '未找到') {
    aftersaleStatus = '状态信息暂时无法获取';
}

if (refundTime === '未找到') {
    refundTime = '时间信息暂时无法获取';
}

if (refundAmount === '未找到') {
    refundAmount = '金额信息暂时无法获取';
}
```

### 8. 性能优化 ⚡

#### 智能等待机制
```javascript
// 使用文档中的轮询等待机制
const maxAttempts = 50; // 10秒超时
const intervalTime = 200;
let attempts = 0;
let pageReady = false;

while (attempts < maxAttempts && !pageReady) {
    attempts++;
    const firstStepItem = shadowRoot.querySelector('.step .step-item:first-child');
    if (firstStepItem) {
        pageReady = true;
        break;
    }
    await new Promise(resolve => setTimeout(resolve, intervalTime));
}
```

### 9. 输出格式标准化 📝

#### 标准输出格式
按照要求的模板格式生成信息：

```
  - 订单售后状态：退款成功
  - 退款时间信息：2024-01-16 10:25:30 退款成功
  - 退款金额与去向：￥99.00 原路退回
```

#### 模板集成
提取的信息直接用于生成标准化的回复模板：

```
亲，为您查询到手机尾号为【xxxx】的订单信息如下：

[订单查询信息]：
  - 订单编号: [订单编号]
  - 下单时间: [下单时间]
  - 订单状态: [订单状态]
  - 订单售后状态：[提取的售后状态]
  - 退款时间信息：[提取的退款时间]
  - 退款金额与去向：[提取的退款金额和去向]

您的订单已经申请退款并且退款已经成功了，退款时间：[退款时间]，请问您遇到什么问题了呢？
```

### 10. 关键改进点 💡

1. **完全基于文档**: 使用文档中验证过的所有提取逻辑
2. **多层级备用**: 主要方法 + 步骤项备用 + 全局备用
3. **智能组合**: 自动组合退款金额和去向信息
4. **详细日志**: 完整的提取过程记录
5. **标准输出**: 按照要求格式输出信息

### 11. 测试验证 🧪

#### 测试场景
- **正常情况**: 页面完全加载，所有信息都能提取
- **部分加载**: 页面部分加载，使用备用方法提取
- **异常情况**: 页面结构变化，使用全局选择器
- **超时情况**: 页面加载超时，使用默认值

#### 验证标准
- 提取的信息格式正确
- 组合的金额和去向信息完整
- 日志记录详细清晰
- 备用方法能够正常工作

## 总结

通过完全基于"退款按钮页面提取功能"文档的代码重构，大幅提升了售后信息提取的准确性和可靠性，确保能够精准提取订单售后状态、退款时间信息、退款金额与去向等关键信息，为用户提供完整准确的订单查询结果。
