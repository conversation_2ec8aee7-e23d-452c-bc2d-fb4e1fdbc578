// ==UserScript==
// @name         微信小商店订单信息提取器
// @namespace    http://tampermonkey.net/
// @version      3.0
// @description  在订单详情页添加一个可移动的面板，用于从Shadow DOM中快速提取并处理信息。
// <AUTHOR> Assistant
// @match        https://store.weixin.qq.com/shop/order/detail?orderid=*
// @grant        GM_addStyle
// @license      MIT
// ==/UserScript==

(function() {
    'use strict';

    // 定义界面样式
    GM_addStyle(`
        #info-panel {
            position: fixed;
            top: 100px;
            left: 20px;
            width: 250px;
            background-color: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            z-index: 9999;
            box-shadow: 0px 0px 10px rgba(0,0,0,0.1);
            font-family: sans-serif;
        }
        #panel-header {
            padding: 8px;
            cursor: move;
            background-color: #f1f1f1;
            border-bottom: 1px solid #ccc;
            color: #333;
            font-size: 14px;
            font-weight: bold;
        }
        #panel-content {
            padding: 10px;
            display: flex;
            flex-direction: column;
        }
        #extract-btn {
            padding: 8px;
            cursor: pointer;
            background-color: #07c160;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
        }
        #info-textarea {
            margin-top: 10px;
            width: 98%;
            height: 80px;
            font-size: 12px;
            border: 1px solid #ddd;
        }
    `);

    // 创建控制面板
    const controlPanel = document.createElement('div');
    controlPanel.id = 'info-panel';

    // 创建面板头部
    const panelHeader = document.createElement('div');
    panelHeader.id = 'panel-header';
    panelHeader.textContent = '信息提取面板';
    controlPanel.appendChild(panelHeader);

    // 创建面板内容区
    const panelContent = document.createElement('div');
    panelContent.id = 'panel-content';
    controlPanel.appendChild(panelContent);

    // 创建提取按钮
    const extractButton = document.createElement('button');
    extractButton.id = 'extract-btn';
    extractButton.textContent = '提取信息';
    panelContent.appendChild(extractButton);

    // 创建信息展示框
    const infoTextarea = document.createElement('textarea');
    infoTextarea.id = 'info-textarea';
    infoTextarea.placeholder = '提取的信息将显示在此处...';
    infoTextarea.readOnly = true;
    panelContent.appendChild(infoTextarea);

    // 将面板添加到页面
    document.body.appendChild(controlPanel);

    // 定义优化的提取事件
    function extractInformation() {
        infoTextarea.value = '正在提取...';
        const microAppHost = document.querySelector('micro-app[name="order"]');

        if (!microAppHost) {
            infoTextarea.value = '提取失败：未找到 micro-app 容器。';
            return;
        }

        const shadowRoot = microAppHost.shadowRoot;
        if (!shadowRoot) {
            infoTextarea.value = '提取失败：无法访问 Shadow DOM。';
            return;
        }

        const targetElement = shadowRoot.querySelector('.status_desc div[data-v-e7459dfe]');
        if (targetElement) {
            const fullText = targetElement.textContent.trim();
            const reasonText = fullText.replace('取消原因：', '').trim();
            infoTextarea.value = reasonText;
        } else {
            infoTextarea.value = '提取失败：未在容器内找到目标信息。';
        }
    }

    // 绑定按钮点击事件
    extractButton.addEventListener('click', extractInformation);

    // 实现面板拖动功能
    let isDragging = false;
    let offsetX, offsetY;

    panelHeader.addEventListener('mousedown', (e) => {
        isDragging = true;
        offsetX = e.clientX - controlPanel.offsetLeft;
        offsetY = e.clientY - controlPanel.offsetTop;
        controlPanel.style.opacity = '0.8';
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;
        let newX = e.clientX - offsetX;
        let newY = e.clientY - offsetY;
        controlPanel.style.left = newX + 'px';
        controlPanel.style.top = newY + 'px';
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
        controlPanel.style.opacity = '1';
    });

})();
